from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
import uuid
from datetime import datetime

# 设置UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 配置日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(pathname)s:%(lineno)d - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

from services.conversation_engine import ConversationEngine
from services.user_manager import UserManager
from models.database import DatabaseManager

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 初始化服务
conversation_engine = ConversationEngine()
db = DatabaseManager()
user_manager = UserManager(db)

# 确保数据库目录存在
os.makedirs(os.path.dirname(db.db_path), exist_ok=True)

@app.route('/')
def index():
    """提供前端页面"""
    return send_from_directory('../frontend', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('../frontend', filename)

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    try:
        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({'error': '消息内容不能为空'}), 400

        user_id = data.get('user_id', str(uuid.uuid4()))
        message = data['message'].strip()

        if not message:
            return jsonify({'error': '消息内容不能为空'}), 400

        # 处理消息
        result = conversation_engine.process_message(user_id, message)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'response_segments': result.get('response_segments', [result['response']]),
            'affection_level': result['affection_level'],
            'affection_change': result['affection_change'],
            'emotion': result['emotion'],
            'memories_extracted': result['memories_extracted'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Chat error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500

@app.route('/api/greeting', methods=['POST'])
def greeting():
    """获取问候消息"""
    try:
        data = request.get_json() or {}
        user_id = data.get('user_id', str(uuid.uuid4()))

        result = conversation_engine.get_greeting_message(user_id)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'response': result['response'],
            'affection_level': result['affection_level'],
            'persona_activity': result['persona_activity'],
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"Greeting error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误，请稍后重试'
        }), 500

@app.route('/api/user/info', methods=['GET'])
def get_user_info():
    """获取用户信息"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'error': '用户ID不能为空'}), 400

        user = db.get_user_or_create(user_id)
        affection_level = db.get_current_affection(user_id)
        recent_conversations = db.get_recent_conversations(user_id, limit=5)
        memories = db.get_relevant_memories(user_id, limit=10)

        return jsonify({
            'success': True,
            'user': user,
            'affection_level': affection_level,
            'recent_conversations': recent_conversations,
            'memories': memories
        })

    except Exception as e:
        print(f"Get user info error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户信息失败'
        }), 500

@app.route('/api/memories', methods=['GET'])
def get_memories():
    """获取用户记忆"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'error': '用户ID不能为空'}), 400

        memories = conversation_engine.memory_manager.get_relevant_memories(user_id, limit=20)

        return jsonify({
            'success': True,
            'memories': memories
        })

    except Exception as e:
        print(f"Get memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取记忆失败'
        }), 500

@app.route('/api/memories/summary', methods=['GET'])
def get_memory_summary():
    """获取用户记忆摘要"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'error': '用户ID不能为空'}), 400

        # 使用Memobase获取用户上下文作为摘要
        summary = conversation_engine.memory_manager.get_user_context(user_id, max_token_size=300)

        return jsonify({
            'success': True,
            'summary': summary
        })

    except Exception as e:
        print(f"Get memory summary error: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取记忆摘要失败'
        }), 500

# 新增记忆管理API
@app.route('/api/memories/stats', methods=['GET'])
def get_memory_stats():
    """获取记忆统计信息"""
    try:
        # 获取用户记忆统计
        user_stats = conversation_engine.memory_manager.get_memory_statistics()

        # 获取个人记忆统计
        persona_stats = conversation_engine.persona_memory_manager.get_memory_stats()

        return jsonify({
            'success': True,
            'user_memories': user_stats,
            'persona_memories': persona_stats
        })
    except Exception as e:
        print(f"Get memory stats error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/search', methods=['GET'])
def search_memories():
    """搜索用户记忆"""
    try:
        user_id = request.args.get('user_id')
        search_query = request.args.get('query', '')
        limit = int(request.args.get('limit', 20))

        if not user_id:
            return jsonify({'error': '用户ID不能为空'}), 400

        if search_query:
            memories = conversation_engine.memory_manager.get_relevant_memories(
                user_id, query_text=search_query, limit=limit
            )
        else:
            memories = conversation_engine.memory_manager.get_relevant_memories(
                user_id, limit=limit
            )

        return jsonify({
            'success': True,
            'memories': memories
        })
    except Exception as e:
        print(f"Search memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/persona', methods=['GET'])
def get_persona_memories():
    """获取个人记忆"""
    try:
        search_query = request.args.get('query', '')
        memory_type = request.args.get('type', None)
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        max_sharing_level = int(request.args.get('max_sharing_level', 5))

        if search_query:
            # 搜索相关记忆
            memories = conversation_engine.persona_memory_manager.get_relevant_persona_memories(
                search_query, user_affection=100, limit=page_size
            )
            result = {
                'success': True,
                'memories': memories,
                'pagination': {
                    'page': 1,
                    'page_size': len(memories),
                    'total': len(memories),
                    'total_pages': 1
                }
            }
        else:
            # 获取所有记忆（简化版本，不支持分页）
            memories = conversation_engine.persona_memory_manager.get_relevant_persona_memories(
                "", user_affection=100, limit=page_size
            )
            result = {
                'success': True,
                'memories': memories,
                'pagination': {
                    'page': page,
                    'page_size': len(memories),
                    'total': len(memories),
                    'total_pages': 1
                }
            }

        return jsonify(result)
    except Exception as e:
        print(f"Get persona memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/memories/clear/<user_id>', methods=['DELETE'])
def clear_user_memories(user_id):
    """清除指定用户的所有记忆（Memobase暂不支持）"""
    try:
        # Memobase暂不支持清除记忆功能
        return jsonify({
            'success': False,
            'message': 'Memobase暂不支持清除记忆功能'
        })
    except Exception as e:
        print(f"Clear memories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 用户管理API
@app.route('/api/users/register', methods=['POST'])
def register_user():
    """用户注册"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        nickname = data.get('nickname', '').strip()
        email = data.get('email', '').strip()

        if not username:
            return jsonify({'error': '用户名不能为空'}), 400

        # 创建用户
        user = user_manager.create_user(
            username=username,
            password=password if password else None,
            nickname=nickname if nickname else None,
            email=email if email else None
        )

        return jsonify({
            'success': True,
            'message': '用户注册成功',
            'user': user
        })

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Register error: {str(e)}")
        return jsonify({'error': '注册失败，请稍后重试'}), 500

@app.route('/api/users/login', methods=['POST'])
def login_user():
    """用户登录"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400

        # 用户认证
        user = user_manager.authenticate_user(username, password)
        if not user:
            return jsonify({'error': '用户名或密码错误'}), 401

        # 创建会话
        session_token = user_manager.create_session(user['user_id'])

        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': user,
            'session_token': session_token
        })

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': '登录失败，请稍后重试'}), 500

@app.route('/api/users/logout', methods=['POST'])
def logout_user():
    """用户登出"""
    try:
        # 从请求头获取会话令牌
        session_token = request.headers.get('Authorization')
        if session_token and session_token.startswith('Bearer '):
            session_token = session_token[7:]  # 移除 "Bearer " 前缀

        if not session_token:
            return jsonify({'error': '会话令牌不能为空'}), 400

        # 登出用户
        success = user_manager.logout_user(session_token)

        if success:
            return jsonify({
                'success': True,
                'message': '登出成功'
            })
        else:
            return jsonify({'error': '登出失败'}), 400

    except Exception as e:
        print(f"Logout error: {str(e)}")
        return jsonify({'error': '登出失败，请稍后重试'}), 500

@app.route('/api/users/profile', methods=['GET'])
def get_user_profile():
    """获取用户资料"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        # 获取详细用户信息
        user_info = user_manager.get_user_info(user['user_id'])
        if not user_info:
            return jsonify({'error': '用户不存在'}), 404

        # 获取用户统计信息
        affection_level = db.get_current_affection(user['user_id'])
        recent_conversations = db.get_recent_conversations(user['user_id'], limit=5)
        memory_summary = conversation_engine.memory_manager.get_user_context(user['user_id'], max_token_size=200)

        return jsonify({
            'success': True,
            'user': user_info,
            'stats': {
                'affection_level': affection_level,
                'conversation_count': len(recent_conversations),
                'memory_summary': memory_summary
            }
        })

    except Exception as e:
        print(f"Get profile error: {str(e)}")
        return jsonify({'error': '获取用户资料失败'}), 500

@app.route('/api/users/profile', methods=['PUT'])
def update_user_profile():
    """更新用户资料"""
    try:
        # 验证会话
        user = validate_session()
        if not user:
            return jsonify({'error': '请先登录'}), 401

        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400

        # 更新用户信息
        success = user_manager.update_user_info(user['user_id'], **data)

        if success:
            return jsonify({
                'success': True,
                'message': '用户资料更新成功'
            })
        else:
            return jsonify({'error': '更新失败'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        print(f"Update profile error: {str(e)}")
        return jsonify({'error': '更新用户资料失败'}), 500

def validate_session():
    """验证会话中间件"""
    session_token = request.headers.get('Authorization')
    if session_token and session_token.startswith('Bearer '):
        session_token = session_token[7:]  # 移除 "Bearer " 前缀

    if not session_token:
        return None

    return user_manager.validate_session(session_token)

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': '虚拟人恋爱陪伴系统'
    })

if __name__ == '__main__':
    print("启动虚拟人恋爱陪伴系统...")
    print("前端地址: http://localhost:8080")
    print("API地址: http://localhost:8080/api")

    app.run(
        host='0.0.0.0',
        port=8082,
        debug=True
    )
