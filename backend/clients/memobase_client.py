"""
Memobase客户端模块
使用Memobase SDK进行用户记忆管理
"""

import logging
from typing import Dict, List, Optional, Any
from config import Config

logger = logging.getLogger(__name__)


class MemobaseClient:
    """Memobase客户端"""

    def __init__(self):
        """初始化Memobase客户端"""
        self.config = Config.MEMOBASE_CONFIG
        self.project_url = self.config.get('project_url', 'http://localhost:8019')
        self.api_key = self.config.get('api_key', 'secret')
        self.project_id = self.config.get('project_id', 'memobase_dev')

        # 初始化Memobase客户端
        self._init_client()

    def _init_client(self):
        """初始化Memobase客户端"""
        try:
            from memobase import MemoBaseClient

            self.client = MemoBaseClient(
                project_url=self.project_url,
                api_key=self.api_key
            )

            # 测试连接
            try:
                self.client.ping()
                logger.info(f"✅ Memobase客户端初始化成功: {self.project_url}")
            except Exception as e:
                logger.warning(f"⚠️ Memobase连接测试失败，但继续初始化: {e}")

        except ImportError:
            logger.error("❌ Memobase SDK未安装，请运行: pip install memobase")
            raise
        except Exception as e:
            logger.error(f"❌ Memobase客户端初始化失败: {e}")
            raise

    def get_or_create_user(self, user_id: str, user_data: Dict = None) -> Any:
        """获取或创建用户"""
        try:
            # 尝试获取现有用户
            try:
                user = self.client.get_user(user_id)
                logger.debug(f"📋 获取现有用户: {user_id}")
                return user
            except:
                # 用户不存在，创建新用户
                user_data = user_data or {"user_id": user_id}
                uid = self.client.add_user(user_data)
                user = self.client.get_user(uid)
                logger.info(f"👤 创建新用户: {user_id}")
                return user
                
        except Exception as e:
            logger.error(f"💥 获取或创建用户失败 - 用户: {user_id}, 错误: {e}")
            raise

    def update_user(self, user_id: str, user_data: Dict) -> bool:
        """更新用户信息"""
        try:
            self.client.update_user(user_id, user_data)
            logger.info(f"📝 更新用户信息: {user_id}")
            return True
        except Exception as e:
            logger.error(f"💥 更新用户信息失败 - 用户: {user_id}, 错误: {e}")
            return False

    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            self.client.delete_user(user_id)
            logger.info(f"🗑️ 删除用户: {user_id}")
            return True
        except Exception as e:
            logger.error(f"💥 删除用户失败 - 用户: {user_id}, 错误: {e}")
            return False

    def insert_chat_data(self, user, messages: List[Dict]) -> str:
        """插入聊天数据"""
        try:
            from memobase import ChatBlob
            
            chat_blob = ChatBlob(messages=messages)
            blob_id = user.insert(chat_blob)
            
            logger.debug(f"💬 插入聊天数据: {len(messages)} 条消息")
            return blob_id
            
        except Exception as e:
            logger.error(f"💥 插入聊天数据失败: {e}")
            raise

    def flush_user_memory(self, user) -> bool:
        """刷新用户记忆"""
        try:
            user.flush()
            logger.info("🔄 用户记忆已刷新")
            return True
        except Exception as e:
            logger.error(f"💥 刷新用户记忆失败: {e}")
            return False

    def get_user_profile(self, user, need_json: bool = False) -> List[Dict]:
        """获取用户画像"""
        try:
            profile = user.profile(need_json=need_json)
            logger.debug(f"👤 获取用户画像: {len(profile) if isinstance(profile, list) else 'JSON格式'}")
            return profile
        except Exception as e:
            logger.error(f"💥 获取用户画像失败: {e}")
            return [] if not need_json else {}

    def get_user_context(self, user, max_token_size: int = 500, prefer_topics: List[str] = None) -> str:
        """获取用户上下文"""
        try:
            context = user.context(
                max_token_size=max_token_size,
                prefer_topics=prefer_topics or []
            )
            logger.debug(f"📄 获取用户上下文: {len(context)} 字符")
            return context
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败: {e}")
            return ""

    def ping(self) -> bool:
        """测试连接"""
        try:
            return self.client.ping()
        except Exception as e:
            logger.error(f"💥 连接测试失败: {e}")
            return False


# 全局Memobase客户端实例
_memobase_client = None


def get_memobase_client() -> MemobaseClient:
    """获取全局Memobase客户端实例"""
    global _memobase_client
    if _memobase_client is None:
        _memobase_client = MemobaseClient()
    return _memobase_client
