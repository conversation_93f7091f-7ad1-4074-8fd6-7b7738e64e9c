"""
基于Memobase的记忆管理模块
使用Memobase SDK进行用户记忆存储和检索
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from clients.memobase_client import get_memobase_client
from config import Config

# 配置日志
logger = logging.getLogger(__name__)


class MemobaseMemoryManager:
    """基于Memobase的记忆管理器"""

    def __init__(self, llm_service=None):
        """
        初始化Memobase记忆管理器

        Args:
            llm_service: LLM服务实例（保留用于兼容性）
        """
        self.llm_service = llm_service
        self.config = Config.MEMOBASE_CONFIG
        
        # 初始化Memobase客户端
        try:
            self.memobase_client = get_memobase_client()
            logger.info("✅ Memobase记忆管理器初始化成功")
        except Exception as e:
            logger.error(f"❌ Memobase记忆管理器初始化失败: {e}")
            raise

        # 记忆类型定义（保持与原系统兼容）
        self.memory_types = {
            'personal': '个人信息',
            'preference': '喜好偏好',
            'experience': '经历体验',
            'goal': '目标计划',
            'relationship': '关系状态',
            'emotion': '情感状态'
        }

        # 重要性等级
        self.importance_levels = {
            'low': 1.0,
            'medium': 2.0,
            'high': 3.0
        }

    def extract_memories_from_text(self, user_id: str, text: str) -> List[Dict]:
        """
        从文本中提取记忆信息（通过Memobase自动处理）

        Args:
            user_id: 用户ID
            text: 输入文本

        Returns:
            提取的记忆信息列表（模拟返回，实际由Memobase处理）
        """
        request_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        logger.info(f"🧠 开始Memobase记忆提取 [{request_id}] - 用户: {user_id}")
        logger.info(f"📝 提取文本: {text}")

        try:
            # 获取或创建用户
            user = self.memobase_client.get_or_create_user(user_id)
            
            # 构造聊天消息格式
            messages = [
                {
                    "role": "user",
                    "content": text,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            # 插入聊天数据到Memobase
            blob_id = self.memobase_client.insert_chat_data(user, messages)
            
            # 如果配置了自动刷新，立即刷新记忆
            if self.config.get('auto_flush', True):
                self.memobase_client.flush_user_memory(user)
            
            logger.info(f"✅ 记忆提取完成 [{request_id}] - Blob ID: {blob_id}")
            
            # 返回模拟的记忆信息（实际记忆由Memobase管理）
            return [{
                'type': 'auto_extracted',
                'content': f"已通过Memobase处理: {text[:50]}...",
                'importance': 1.0,
                'timestamp': datetime.now().isoformat()
            }]
            
        except Exception as e:
            logger.error(f"💥 Memobase记忆提取失败 [{request_id}]: {str(e)}")
            return []

    def save_memory(self, user_id: str, memory_type: str, content: str, importance: float = 1.0):
        """
        保存记忆到Memobase

        Args:
            user_id: 用户ID
            memory_type: 记忆类型
            content: 记忆内容
            importance: 重要程度
        """
        try:
            # 获取或创建用户
            user = self.memobase_client.get_or_create_user(user_id)
            
            # 构造记忆消息格式
            messages = [
                {
                    "role": "system",
                    "content": f"记忆类型: {memory_type}, 重要性: {importance}",
                },
                {
                    "role": "user", 
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                }
            ]
            
            # 插入到Memobase
            blob_id = self.memobase_client.insert_chat_data(user, messages)
            
            # 如果配置了自动刷新，立即刷新记忆
            if self.config.get('auto_flush', True):
                self.memobase_client.flush_user_memory(user)
            
            logger.info(f"💾 记忆已保存到Memobase - 用户: {user_id}, 类型: {memory_type}, 重要性: {importance}")
            
        except Exception as e:
            logger.error(f"💥 记忆保存失败 - 用户: {user_id}, 错误: {str(e)}")

    def get_relevant_memories(self, user_id: str, query_text: str = None, keywords: List[str] = None,
                            limit: int = None, similarity_threshold: float = None) -> List[Dict]:
        """
        获取相关记忆（基于Memobase的用户画像）

        Args:
            user_id: 用户ID
            query_text: 查询文本（用于上下文生成）
            keywords: 关键词列表（备用）
            limit: 返回数量限制
            similarity_threshold: 相似度阈值（保留用于兼容性）

        Returns:
            相关记忆列表
        """
        try:
            # 获取用户
            user = self.memobase_client.get_or_create_user(user_id)
            
            # 获取用户画像
            profile = self.memobase_client.get_user_profile(user, need_json=False)
            
            # 转换为兼容格式
            memories = []
            for item in profile:
                if hasattr(item, 'topic') and hasattr(item, 'content'):
                    memories.append({
                        'content': item.content,
                        'type': item.topic,
                        'sub_type': getattr(item, 'sub_topic', ''),
                        'importance': 2.0,  # 默认重要性
                        'created_at': getattr(item, 'created_at', datetime.now().isoformat()),
                        'similarity': 1.0,  # Memobase已处理相关性
                        'recall_count': 1
                    })
            
            # 应用限制
            if limit:
                memories = memories[:limit]
            
            logger.info(f"🔍 Memobase获取记忆 - 用户: {user_id}, 结果: {len(memories)} 条")
            return memories
            
        except Exception as e:
            logger.error(f"💥 获取Memobase记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return []

    def get_user_context(self, user_id: str, max_token_size: int = None, prefer_topics: List[str] = None) -> str:
        """
        获取用户上下文（用于系统提示词）

        Args:
            user_id: 用户ID
            max_token_size: 最大token数
            prefer_topics: 优先话题

        Returns:
            格式化的用户上下文字符串
        """
        try:
            # 获取用户
            user = self.memobase_client.get_or_create_user(user_id)
            
            # 使用配置的默认值
            max_token_size = max_token_size or self.config.get('max_token_size', 500)
            prefer_topics = prefer_topics or self.config.get('prefer_topics', [])
            
            # 获取Memobase生成的上下文
            context = self.memobase_client.get_user_context(
                user, 
                max_token_size=max_token_size,
                prefer_topics=prefer_topics
            )
            
            logger.debug(f"📄 获取用户上下文 - 用户: {user_id}, 长度: {len(context)}")
            return context
            
        except Exception as e:
            logger.error(f"💥 获取用户上下文失败 - 用户: {user_id}, 错误: {str(e)}")
            return ""

    def flush_user_memory(self, user_id: str) -> bool:
        """
        手动刷新用户记忆

        Args:
            user_id: 用户ID

        Returns:
            是否成功
        """
        try:
            user = self.memobase_client.get_or_create_user(user_id)
            return self.memobase_client.flush_user_memory(user)
        except Exception as e:
            logger.error(f"💥 刷新用户记忆失败 - 用户: {user_id}, 错误: {str(e)}")
            return False

    def get_memory_statistics(self) -> Dict:
        """
        获取记忆统计信息（简化版本）

        Returns:
            记忆统计信息
        """
        try:
            # Memobase不直接提供统计信息，返回基本信息
            return {
                'total_memories': 0,  # 无法直接获取
                'by_user': {},
                'by_type': {},
                'avg_importance': 0.0,
                'provider': 'memobase'
            }
        except Exception as e:
            logger.error(f"💥 获取记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'by_user': {},
                'by_type': {},
                'avg_importance': 0.0,
                'provider': 'memobase',
                'error': str(e)
            }
